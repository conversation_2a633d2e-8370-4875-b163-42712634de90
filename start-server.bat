@echo off
echo QR Code Scanner - Local Server
echo ================================

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Trying alternative methods...
    goto :try_node
)

echo Starting Python HTTPS server...
python start-server.py
goto :end

:try_node
REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js not found. Trying simple HTTP server...
    goto :try_simple
)

echo Starting Node.js server...
echo const https = require('https'); > temp_server.js
echo const fs = require('fs'); >> temp_server.js
echo const path = require('path'); >> temp_server.js
echo const http = require('http'); >> temp_server.js
echo. >> temp_server.js
echo const server = http.createServer((req, res) => { >> temp_server.js
echo   let filePath = path.join(__dirname, req.url === '/' ? 'index.html' : req.url); >> temp_server.js
echo   fs.readFile(filePath, (err, data) => { >> temp_server.js
echo     if (err) { res.writeHead(404); res.end('Not found'); return; } >> temp_server.js
echo     res.writeHead(200); res.end(data); >> temp_server.js
echo   }); >> temp_server.js
echo }); >> temp_server.js
echo server.listen(8080, () => console.log('Server at http://localhost:8080')); >> temp_server.js

node temp_server.js
del temp_server.js
goto :end

:try_simple
echo Starting simple Python HTTP server...
python -m http.server 8080
goto :end

:end
echo.
echo Server stopped.
pause

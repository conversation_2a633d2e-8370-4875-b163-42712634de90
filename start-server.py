#!/usr/bin/env python3
"""
Simple HTTPS server for testing QR code scanner
Run this script to serve the application over HTTPS on localhost
"""

import http.server
import ssl
import socketserver
import os
import sys
from pathlib import Path

# Configuration
PORT = 8443
HOST = 'localhost'

def create_self_signed_cert():
    """Create a self-signed certificate for HTTPS"""
    try:
        import subprocess
        
        # Check if certificate already exists
        if os.path.exists('server.crt') and os.path.exists('server.key'):
            print("✓ SSL certificate already exists")
            return True
            
        print("Creating self-signed SSL certificate...")
        
        # Create self-signed certificate using openssl
        cmd = [
            'openssl', 'req', '-x509', '-newkey', 'rsa:4096', '-keyout', 'server.key',
            '-out', 'server.crt', '-days', '365', '-nodes', '-subj',
            f'/C=US/ST=State/L=City/O=Organization/CN={HOST}'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ SSL certificate created successfully")
            return True
        else:
            print("✗ Failed to create SSL certificate")
            print("Error:", result.stderr)
            return False
            
    except FileNotFoundError:
        print("✗ OpenSSL not found. Please install OpenSSL or use the HTTP server instead.")
        return False
    except Exception as e:
        print(f"✗ Error creating certificate: {e}")
        return False

def start_https_server():
    """Start HTTPS server"""
    if not create_self_signed_cert():
        return False
        
    try:
        # Create SSL context
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('server.crt', 'server.key')
        
        # Create server
        with socketserver.TCPServer((HOST, PORT), http.server.SimpleHTTPRequestHandler) as httpd:
            httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
            
            print(f"✓ HTTPS Server running at https://{HOST}:{PORT}/")
            print("✓ Camera access should work with HTTPS")
            print("✓ Press Ctrl+C to stop the server")
            print("\n" + "="*50)
            print("Open your browser and navigate to:")
            print(f"https://{HOST}:{PORT}/")
            print("="*50 + "\n")
            
            httpd.serve_forever()
            
    except Exception as e:
        print(f"✗ Error starting HTTPS server: {e}")
        return False

def start_http_server():
    """Start HTTP server (camera won't work, but file upload will)"""
    try:
        with socketserver.TCPServer((HOST, PORT), http.server.SimpleHTTPRequestHandler) as httpd:
            print(f"✓ HTTP Server running at http://{HOST}:{PORT}/")
            print("⚠ Camera access won't work with HTTP (use file upload instead)")
            print("✓ Press Ctrl+C to stop the server")
            print("\n" + "="*50)
            print("Open your browser and navigate to:")
            print(f"http://{HOST}:{PORT}/")
            print("="*50 + "\n")
            
            httpd.serve_forever()
            
    except Exception as e:
        print(f"✗ Error starting HTTP server: {e}")
        return False

def main():
    """Main function"""
    print("QR Code Scanner - Local Server")
    print("="*40)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--http':
        print("Starting HTTP server (camera won't work)...")
        start_http_server()
    else:
        print("Starting HTTPS server (recommended for camera access)...")
        if not start_https_server():
            print("\nFalling back to HTTP server...")
            start_http_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n✓ Server stopped")
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")

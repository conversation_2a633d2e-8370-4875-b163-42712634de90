// QR Code Scanner Implementation
class QRScanner {
    constructor() {
        this.html5QrCode = null;
        this.isScanning = false;
        this.lastScannedResult = null;
        
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        this.overlay = document.getElementById('qr-scanner-overlay');
        this.modal = document.getElementById('qr-scanner-modal');
        this.closeBtn = document.getElementById('qr-scanner-close');
        this.cancelBtn = document.getElementById('qr-scanner-cancel');
        this.okBtn = document.getElementById('qr-scanner-ok');
        this.readerDiv = document.getElementById('qr-reader');
        this.messageDiv = document.getElementById('qr-scanner-message');
        this.resultDiv = document.getElementById('qr-scanner-result');
        this.resultContent = document.getElementById('qr-result-content');
        this.qrFab = document.getElementById('qr-fab');
    }

    bindEvents() {
        // QR FAB click event
        if (this.qrFab) {
            this.qrFab.addEventListener('click', () => this.openScanner());
        }

        // Close events
        this.closeBtn.addEventListener('click', () => this.closeScanner());
        this.cancelBtn.addEventListener('click', () => this.closeScanner());
        this.okBtn.addEventListener('click', () => this.closeScanner());
        
        // Overlay click to close
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.closeScanner();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.overlay.classList.contains('active')) {
                this.closeScanner();
            }
        });
    }

    async openScanner() {
        try {
            // Show the modal
            this.overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Reset UI state
            this.resetUI();
            
            // Initialize the scanner
            await this.initializeScanner();
            
        } catch (error) {
            console.error('Error opening QR scanner:', error);
            this.showError('Failed to access camera. Please check permissions.');
        }
    }

    async initializeScanner() {
        if (this.html5QrCode) {
            await this.stopScanner();
        }

        this.html5QrCode = new Html5Qrcode("qr-reader");
        
        const config = {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0,
            disableFlip: false,
            rememberLastUsedCamera: true
        };

        try {
            // Try to get cameras
            const cameras = await Html5Qrcode.getCameras();
            
            if (cameras && cameras.length > 0) {
                // Prefer back camera if available
                let cameraId = cameras[0].id;
                const backCamera = cameras.find(camera => 
                    camera.label.toLowerCase().includes('back') || 
                    camera.label.toLowerCase().includes('rear') ||
                    camera.label.toLowerCase().includes('environment')
                );
                
                if (backCamera) {
                    cameraId = backCamera.id;
                }

                // Start scanning
                await this.html5QrCode.start(
                    cameraId,
                    config,
                    (decodedText, decodedResult) => {
                        this.onScanSuccess(decodedText, decodedResult);
                    },
                    (errorMessage) => {
                        // Handle scan errors silently - this is normal during scanning
                        // console.log('Scan error:', errorMessage);
                    }
                );
                
                this.isScanning = true;
                this.showMessage('Position the QR code within the frame to scan');
                
            } else {
                throw new Error('No cameras found');
            }
            
        } catch (error) {
            console.error('Error starting scanner:', error);
            this.showError('Failed to start camera. Please check permissions and try again.');
        }
    }

    async onScanSuccess(decodedText, decodedResult) {
        if (this.lastScannedResult === decodedText) {
            return; // Prevent duplicate scans
        }
        
        this.lastScannedResult = decodedText;
        
        try {
            // Stop scanning
            await this.stopScanner();
            
            // Show success result
            this.showResult(decodedText);
            
            // Add haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(200);
            }
            
        } catch (error) {
            console.error('Error handling scan result:', error);
        }
    }

    async stopScanner() {
        if (this.html5QrCode && this.isScanning) {
            try {
                await this.html5QrCode.stop();
                this.isScanning = false;
            } catch (error) {
                console.error('Error stopping scanner:', error);
            }
        }
    }

    async closeScanner() {
        try {
            // Stop the scanner
            await this.stopScanner();
            
            // Clear the scanner instance
            if (this.html5QrCode) {
                this.html5QrCode.clear();
                this.html5QrCode = null;
            }
            
            // Hide the modal
            this.overlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // Reset state
            this.lastScannedResult = null;
            
        } catch (error) {
            console.error('Error closing scanner:', error);
            // Force close anyway
            this.overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    resetUI() {
        this.messageDiv.style.display = 'block';
        this.resultDiv.style.display = 'none';
        this.okBtn.style.display = 'none';
        this.cancelBtn.textContent = 'Cancel';
        this.readerDiv.innerHTML = '';
    }

    showMessage(text) {
        const textElement = this.messageDiv.querySelector('.qr-scanner-text');
        if (textElement) {
            textElement.textContent = text;
        }
        this.messageDiv.style.display = 'block';
        this.resultDiv.style.display = 'none';
    }

    showResult(decodedText) {
        this.messageDiv.style.display = 'none';
        this.resultDiv.style.display = 'block';
        this.resultContent.textContent = decodedText;
        this.okBtn.style.display = 'inline-flex';
        this.cancelBtn.textContent = 'Close';
    }

    showError(message) {
        this.showMessage(message);
        const iconElement = this.messageDiv.querySelector('.material-icons');
        if (iconElement) {
            iconElement.textContent = 'error';
            iconElement.style.color = 'var(--error-color)';
        }
    }
}

// Initialize QR Scanner when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.qrScanner = new QRScanner();
});
